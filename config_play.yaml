# 日志配置
logging:
  level: "DEBUG"  # 全局日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  console_level: "DEBUG" # 控制台输出级别
  file_level: "DEBUG"    # 文件输出级别

# 游戏相关配置
game:
  min_interval_seconds: 1  # 同一玩家两次游戏最小间隔(秒)，默认120 [动态加载]
  possibilities:
    first_time: 1.0          # 首次游戏概率，默认1 [动态加载]
    after_no_prize: 1      # 玩过，但没抓到过奖品，默认1 [动态加载]
    # after_prize: 0.0         # [旧配置，已被 after_wins_probabilities 替换] 抓到过奖品，再玩的概率
    # 新增：根据已中奖次数设置的概率列表
    # 列表的第1个值对应已中奖1次后的概率，第2个值对应已中奖2次后的概率，以此类推。
    # 如果中奖次数超过列表长度，则使用列表最后一个值。
    after_wins_probabilities: [0.5, 0.0, 0.0] # [动态加载]
    # paid_bonus: 0.2          # [功能已移除] 付费玩家额外概率加成(百分比)
  max_free_games_per_session: 10    # 每个会话玩家可用的最大免费游戏次数 [动态加载]
  z_offset_extra: 50.0       # 在real模式下，当概率判断玩家应该抓不中时，发送给移动模块的Z轴额外偏移值(毫米)；如果应该抓中，则发送0 [动态加载]
  
  # 优先级系数相关配置
  priority:
    paid_multiplier: 5.0     # 付费玩家优先系数乘数，默认5 [动态加载]

    # 评论次数影响系数：pca * 根号 （pcb+x）（例如发24次就能乘以5）
    comment:
      pca: 1.0               # 评论次数系数A，默认1 [动态加载]
      pcb: 1.0               # 评论次数系数B，默认1 [动态加载]

    # 游戏次数影响系数:ppa * ppb ^(-x)（从0次到10次，输出分别是5、3.33、2.22、1.48、0.98，直到0.85。
    played_times:
      ppa: 5.0               # 游戏次数系数A，默认5 [动态加载]
      ppb: 1.5               # 游戏次数系数B，默认1.5 [动态加载]

    # 停留时间影响系数:pta + ptb * ln(C)（从1到10分钟，输出是1、1.69、2.09、2.38、2.60、2.79、直到3.3，100分钟也就是5.6）
    stay_time: #分钟数，最小为1
      pta: 1.0               # 停留时间系数A，默认1 [动态加载]
      ptb: 1.0               # 停留时间系数B，默认1 [动态加载]

    # 奖品状态影响系数
    prize_status:
      no_prize: 3.0          # 没拿过奖的乘数，默认3 [动态加载]
      has_prize: 1.0         # 拿过奖的乘数，默认1 [动态加载]

# 数据库配置
database:
  db_file: "Play_db.db"      # 数据库文件路径
  session_expire_hours: 72    # 会话过期时间(小时)
  sync_interval_seconds: 10   # 内存数据同步到数据库的间隔时间(秒)
  batch_size: 100            # 每批处理的数据条数
  snapshot_timeout: 2        # 获取数据快照的超时时间(秒)

# 游戏线程配置
game_thread:
  empty_queue_sleep: 0.5            # 队列为空时游戏线程的休眠时间（秒），对 simulate 和real模式都有效；
  # 在 real 模式下，如果当前有游戏正在等待运动模块的响应，游戏处理线程也会使用这个休眠时间来等待，避免立即尝试处理下一个队列项目
  post_game_sleep: 0.5              # 处理完一个游戏后游戏线程的休眠时间（秒）
  #在 simulate 模式下，它是在模拟游戏（play_logic.go_play）执行完成并调用 _handle_game_completion 之后发生的休眠。
  #在 real 模式下，它是在向运动模块发送抓取指令之后（无论指令是否成功发送）发生的休眠。如果指令发送成功，实际的抓取过程会异步进行，这个休眠是主游戏处理循环在尝试获取下一个队列项目之前的暂停。
  error_sleep: 1.0                  # 游戏线程发生错误后的休眠时间（秒），真实、虚拟两种模式都用
  game_timeout: 30                  # 游戏超时时间（秒），仅对 simulate 模式有效

# 消息服务器配置
message_server:
  host: '127.0.0.1'
  port: 9999
  route: '/game-da302d82'

# 网络服务配置（修改端口号，确保与Move_Main.py的GameServiceServer端口一致）
motormove_service:
  host: 'localhost'     # 服务器主机名或IP地址
  port: 5556           # Move_Main.py 作为服务端的端口号（与GameServiceServer一致）
# 检测服务器通信配置
Detect_server:
  enabled: true                    # 是否启用检测功能
  host: 'localhost'               # 服务器主机名或IP地址
  port: 5555                      # 服务器端口号
  connection_timeout: 5.0         # 连接超时时间（秒）
  receive_timeout: 1.0            # 接收数据超时时间（秒）
  reconnect_delay: 1.0            # 初始重连延迟（秒）
  max_reconnect_delay: 30.0       # 最大重连延迟（秒）
  log_stats_interval: 100         # 每多少条数据记录一次统计日志
  
# 轮询间隔（秒）
poll_interval: 1
# 心跳间隔（秒）
heartbeat_interval: 1
# 原始日志文件
raw_log_file: 'receive_raw.log'
# 是否记录所有消息类型
record_all_message_types: true

# OBS配置
obs:
  host: "localhost"
  port: 4455
  password: ""  # 如果有密码，填在这里
  update_interval: 0.08  # OBS显示更新最小间隔（秒） [动态加载]
  congrats_video_source: "抓中特效"  # OBS中祝贺视频源的名称 [动态加载]
  congrats_duration: 2  # 祝贺视频提示时长（秒） [动态加载]

# GUI显示配置
displayer:
  enabled: true                   # 是否启用此GUI窗口
  tcp_port: 5557                   # GUI进程TCP通信端口
  window:
    title: "游戏助手实时信息面板" # 窗口标题
    default_width_ratio: 0.9      # 窗口默认宽度占屏幕宽度的百分比
    default_height_ratio: 0.8     # 窗口默认高度占屏幕高度的百分比
    background_color: "#2B2B2B"   # 整个窗口的背景颜色
  layout:
    left_panel_ratio: 0.60        # 左侧面板占总窗口宽度的百分比
  
  # --- 修改后的字体配置 ---
  fonts:
    # 基础字体定义 (仅字体族和字重)
    player_font:
      family: "Microsoft YaHei UI" #Segoe UI Emoji Regular
      weight: "bold"
    data_font:
      family: "Microsoft YaHei UI"
      weight: "bold"

    # 新增：全局颜色定义
    global_colors:
      player_name: "#df0f2d"   # 全局玩家名颜色 (红色) [动态加载]

    # 各UI区域的独立字体大小和颜色配置
    current_player:
      size: 24
      color: "#30ef1a" # 绿色 (用于非玩家名部分)
    
    status_info: # 用于"抓到物品"、"排队人数"、"FPS"
      size: 24
      color: "#30ef1a" # 绿色

    queue_list: # 左侧排队列表
      size: 20
      color: "#07ccfb" # 排队列表中非玩家名部分的颜色

    activity_log: # 右侧活动日志
      size: 20 # 日志区域所有文本使用统一大小
      colors:
        # player_name: "#eb24c1"   # 已移至 global_colors
        comment_text: "#30ef1a"  # 评论内容颜色 (绿色)
        default_text: "#07ccfb"  # 普通文本颜色 (蓝色)

  text_formatting: # 用于指导文本截断和对齐的字符数
    max_history_rewards_length: 50  # 历史奖励字符串最大显示长度 (字符数)
    max_active_name_length: 10      # 右侧活动日志中玩家名字最大长度 (汉字数)
    max_active_comment_length: 40   # 右侧活动日志中评论内容最大长度 (字符数)
  active_players_max_lines: 30      # 右侧活动日志区域显示的最大行数
  fps_data_timeout: 2.0             # FPS数据过期时间（秒），超过此时间显示"--"

# 订单系统配置
orders:
  enabled: false                        # 是否启用订单系统
  fetch_interval_seconds: 5          # 抓取订单的时间间隔（秒）
  check_earliest_creation_time: true   # 是否根据最早创建时间过滤订单
  order_time_buffer_seconds: 5         # 增量抓取订单设定的创建时间会比上次抓取的最新订单更提前这些秒数。是个保守策略，以免以为时间误差而漏单。（秒）

# Web显示配置（供OBS捕捉，显示游戏进程和排队）
web_display:
  enabled: true
  tcp_port: 5558        # TCP 指令接收端口
  websocket_port: 5560  # WebSocket 服务器端口

  # 新增：HTTP服务器配置，供OBS浏览器源使用
  http_server: # 
    enabled: true # 是否为OBS启用HTTP服务 
    port: 5559    # HTTP服务的端口号

  # 本地预览窗口配置（可选，主要用于调试）
  local_window: # 
    enabled: true # 是否显示本地预览窗口
    window_title: "游戏提示词窗口 [本地预览]" # 
    window_width: 1080 # 
    window_height: 1080 # 
    on_top: false # 
    frameless: false # 

  window_title: "游戏助手实时信息面板" # HTML页签标题 
  background_color: "rgba(0,255,237,1)" # 整体背景色 [动态加载]
  grab_area_canvas_z_index: 30 # 抓取区域Canvas的层级 [动态加载]
  text_wrapper_z_index: 20 # 文本显示区域的层级 [动态加载]

  # 三种显示状态的独立配置
  display_settings: # 
    # 修改：使用更直观的中心点定位
    position: # 
      # [水平中心位置百分比, 垂直中心位置百分比]
      center: ["50%", "42%"] # [动态加载]
      width: "80%" # 新增：提示词容器的宽度，可以是百分比或像素值 

    # 1. 玩家正在游戏中
    playing: # [动态加载]
      template: "{player}在抓！\n为TA加油！" # [动态加载]
      regular_font_file: "DouyinSansBold.otf" # 
      emoji_font_file: "segoe-ui-emoji.ttf" # 
      font_size: "90px" # [动态加载]
      font_color: "#00FF00" # [动态加载]
      stroke_color: "#000000" # [动态加载]
      stroke_width: "10px" # [动态加载]
      shadow_blur: "15px" # 阴影模糊半径，用于OBS兼容性 [动态加载]
      duration: 1  # 持续显示，直到被其他消息替换 [动态加载]
      z_index: 20 # [动态加载]
      animated: false  # 游戏中提示不使用动画 [动态加载]

    # 2. 玩家未抓中（nothing）
    caught_nothing: # [动态加载]
      template: "差一点！{player}继续努力！" # [动态加载]
      regular_font_file: "DouyinSansBold.otf" # 
      emoji_font_file: "segoe-ui-emoji.ttf" # 
      font_size: "90px" # [动态加载]
      font_color: "#FFFF00" # [动态加载]
      stroke_color: "#333333" # [动态加载]
      stroke_width: "10px" # [动态加载]
      shadow_blur: "20px" # 阴影模糊半径，用于OBS兼容性 [动态加载]
      duration: 1  # 显示2秒 [动态加载]
      z_index: 21 # [动态加载]
      animated: false  # 失败提示不使用动画 [动态加载]

    # 3. 玩家成功抓中
    caught_item: # [动态加载]
      # 修改：将主消息的配置移入 main_line
      main_line: # [动态加载]
        template: "祝贺{player}抓中了{item}！" # [动态加载]
        regular_font_file: "DouyinSansBold.otf" # 
        emoji_font_file: "segoe-ui-emoji.ttf" # 
        font_size: "90px" # [动态加载]
        font_color: "#FF0000" # 之前是粉色FF69B4 [动态加载]
        stroke_color: "#333333" # [动态加载]
        stroke_width: "10px" # [动态加载]
        shadow_blur: "10px" # 阴影模糊半径，用于OBS兼容性 [动态加载]
        duration: 3  # 主消息显示3秒 [动态加载]
        z_index: 22 # [动态加载]
        animated: true  # 成功抓中使用动画 [动态加载]
        # 动画参数
        pulse_scale_from: 0.9 # [动态加载]
        pulse_scale_to: 1.1 # [动态加载]
        pulse_opacity_from: 1.0 # [动态加载]
        pulse_opacity_to: 1.0 # [动态加载]
        pulse_duration_seconds: 0.3 # [动态加载]

      # 附加提示行现在与 main_line 是平级关系，结构更清晰
      sub_lines: # [动态加载]
        lines: # [动态加载]
          - enabled: true # [动态加载]
            template1: "获得满减20元券！\n再抓到一次优惠翻倍！" # [动态加载]
            template2: "获得满减40元券！\n再抓到一次优惠翻倍！" # [动态加载]
            template3: "获得满减80元券！\n再抓到一次优惠翻倍！" # [动态加载]
            template4: "获得满减160元券！\n再抓到一次优惠翻倍！" # [动态加载]
            font_file: "DouyinSansBold.otf" # 
            font_size: "45px" # [动态加载]
            font_color: "#FF1C00" # [动态加载]
            background_color: "rgba(255, 255, 0, 1)" # [动态加载]
            stroke_color: "#000000" # [动态加载]
            stroke_width: "0px" # [动态加载]
            shadow_blur: "5px" # 阴影模糊半径，用于OBS兼容性 [动态加载]
            z_index: 22 # [动态加载]
            animated: true # [动态加载]
            margin_top: "15px" # 与上一行间距 [动态加载]
            duration: 3 # 此行独立显示3秒 [动态加载]
          - enabled: true # [动态加载]
            template: "点击右下商品->客服领取" # [动态加载]
            font_file: "DouyinSansBold.otf" # 
            font_size: "40px" # [动态加载]
            font_color: "#FFFFFF" # [动态加载]
            background_color: "rgba(100, 100, 100, 0.5)" # [动态加载]
            stroke_color: "#000000" # [动态加载]
            stroke_width: "5px" # [动态加载]
            shadow_blur: "5px" # 阴影模糊半径，用于OBS兼容性 [动态加载]
            z_index: 22 # [动态加载]
            animated: false # [动态加载]
            margin_top: "10px" # 与上一行间距 [动态加载]
            duration: 3 # 此行独立显示3秒 [动态加载]

    # 4. 玩家等待订单
    player_waiting_order: # [动态加载]
      enabled: true # [动态加载]
      template: "{player}免费已用完！" # [动态加载]
      regular_font_file: "DouyinSansBold.otf" # 
      emoji_font_file: "segoe-ui-emoji.ttf" # 
      font_size: "90px" # [动态加载]
      font_color: "#00FF00" # [动态加载]
      stroke_color: "#000000" # [动态加载]
      stroke_width: "10px" # [动态加载]
      shadow_blur: "15px" # 阴影模糊半径，用于OBS兼容性 [动态加载]
      duration: 2  # 持续显示，直到被其他消息替换 [动态加载]
      z_index: 20 # [动态加载]
      animated: false  # 游戏中提示不使用动画 [动态加载]
      center: ["80%", "42%"] # [动态加载]
      width: "85%" #提示词容器的宽度，可以是百分比或像素值 


  # 排队列表显示配置
  queue_display: # [动态加载]
    enabled: true # [动态加载]
    position_x: "50px"       # 左边距 [动态加载]
    position_y: "140px"       # 上边距 [动态加载]
    z_index: 10             # 层级（低于祝贺词） [动态加载]
    font_size: "24px"       # 字体大小 [动态加载]
    font_color: "#ffffff"    # 字体颜色 [动态加载]
    stroke_color: "#000000"  # 描边颜色 [动态加载]
    stroke_width: "4px"      # 描边宽度 [动态加载]
    shadow_blur: "4px"      # 阴影模糊半径，用于OBS兼容性 [动态加载]

    # 新增：付费玩家的特殊样式
    paid_player_style: # [动态加载]
      bold: true             # 是否加粗 [动态加载]
      font_color: "#FFD700"   # 字体颜色 (可选, 不填则使用默认) [动态加载]

    max_items: 10            # 最大显示条目数 [动态加载]
    border_radius: "8px"     # 圆角 [动态加载]
    padding: "10px"         # 内边距 [动态加载]
    # 字体文件（可与祝贺文本使用不同字体）
    regular_font_file: "msyhbd.ttc" # 
    emoji_font_file: "segoe-ui-emoji.ttf" #

    # 表格样式配置
    table_spacing: "6px"     # 行间距 [动态加载]
    column_widths: # [动态加载]
      index: "60px"          # 序号列宽度 [动态加载]
      name: "180px"          # 姓名列宽度 [动态加载]
      comments: "70px"       # 评论数列宽度 [动态加载]
      time: "70px"           # 时长列宽度 [动态加载]

    # 表头文本
    headers: # [动态加载]
      show: true # [动态加载]
      text_before: "排队朋友" # [动态加载]
      text_before_font_size_multiplier: 1.5 # 头部文本字体大小乘数 [动态加载]
      text_before_margin_left_px: 150      # 头部文本左边距（像素） [动态加载]
      text_after: "加关注、多评论、停留长的朋友排队优先！\n重复评论不算；玩过后评论重新计数" # [动态加载]
      columns: # [动态加载]
        index: "排位" # [动态加载]
        name: "网名" # [动态加载]
        comments: "评论" # [动态加载]
        time: "停留分钟" # [动态加载]

  # 新增：抓取区域可视化配置
  grab_area: # [动态加载]
    enabled: true                     # 是否启用抓取区域可视化总开关 [动态加载]

    # 源归一化坐标点，用于透视变换。通常是 [[0,0], [1,0], [1,1], [0,1]]
    # 顺序必须与 Config_livecam.yaml 中 roi_points 的点一一对应
    # 例如：[左上, 右上, 右下, 左下] -> [点1, 点2, 点3, 点4]
    source_normalized_points: # [动态加载]
      - [0.0, 0.0] # 对应 roi_points[0] [动态加载]
      - [1.0, 0.0] # 对应 roi_points[1] [动态加载]
      - [1.0, 1.0] # 对应 roi_points[2] [动态加载]
      - [0.0, 1.0] # 对应 roi_points[3] [动态加载]

    roi_boundary_display: # [动态加载]
      enabled: true                   # 是否显示ROI边界线 [动态加载]
      color: "rgba(255, 255, 0, 1)" # ROI边界线条颜色 (CSS color string) [动态加载]
      line_width: 10                   # ROI边界线条宽度 (pixels) [动态加载]

    object_display: # [动态加载]
      enabled: true                   # 是否显示物体包围框和标签 [动态加载]
      # 不同类别物体的包围框颜色，key为class_id (整数)
      bounding_box_colors_by_class_id: # [动态加载]
        0: "rgba(0, 255, 0, 0.7)"     # 例如：类别0的物体用绿色 [动态加载]
        1: "rgba(255, 0, 255, 0.7)"   # 例如：类别1的物体用洋红色 [动态加载]
        2: "rgba(255, 0, 255, 0.7)"   # 例如：类别1的物体用洋红色 [动态加载]
      default_bounding_box_color: "rgba(255, 165, 0, 0.7)" # 未在上面指定颜色的物体的默认颜色 [动态加载]
      bounding_box_line_width: 10     # 物体包围框线条宽度 (pixels) [动态加载]

    label_display: # [动态加载]
      enabled: true                   # 是否显示物体ID标签 [动态加载]
      font_family: "Arial, sans-serif" # 标签字体 
      font_size_px: 50                # 标签字体大小 (pixels) [动态加载]
      font_color: "rgba(255, 255, 255, 1)" # 标签字体颜色 [动态加载]
      offset_x_px: 0                  # 标签相对于智能锚点的水平偏移 (正数向右，负数向左) [动态加载]
      offset_y_px: 0                  # 标签相对于智能锚点的垂直偏移 (正数向下，负数向上) [动态加载]
      # 智能锚点说明：根据旋转角度自动选择最合适的边缘中心点
      # 0度: 顶边中心, 90度: 左边中心, 180度: 底边中心, 270度: 右边中心
      stroke_color: "rgba(0, 0, 0, 1)"  # 标签描边颜色 [动态加载]
      stroke_width_px: 10                # 标签描边宽度 (pixels) [动态加载]

    view_transform: # [动态加载]
      scale: 0.75                      # 整体缩放比例 (例如0.5表示缩小一半, 1.0表示原始大小) [动态加载]
      rotation_degrees: 90.0           # 顺时针旋转角度 (0, 90, -90, etc.) [动态加载]
      # 抓取区域的逻辑中心点在Webview画布上的相对位置 (0.0-1.0)
      # 例如 [0.5, 0.5] 表示在Webview的正中央
      center_on_webview_ratio: [0.52, 0.685] # [动态加载]

# 新增：虚拟玩家配置
virtual_player:
  enabled: true                                 # 是否启用虚拟玩家功能
  list_filename: "托玩家列表.txt"                 # 虚拟玩家名单文件路径
  min_real_players_in_queue: 2                  # 触发填充模式的真实玩家数量阈值
  stage_for_real_players: 7                     # 队列人数目标上限及稀疏模式切换阈值
  active_pool_size: 5                           # 活跃虚拟玩家池的大小
  max_games_per_player: 3                       # 单个虚拟玩家最大游戏次数
  check_interval_seconds: 4                    # 仅在真实玩家很少，需要填充虚拟玩家的时候起作用；隔一段时间检查是否需要添加虚拟玩家（秒）

