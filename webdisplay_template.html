<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{WINDOW_TITLE}}</title>
    <style>
        {{ALL_FONT_FACES}}

        html, body {
            height: 100%;
            width: 100%;
            overflow: hidden;
        }
        body {
            margin: 0;
            padding: 0;
            background-color: {{BACKGROUND_COLOR}};
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-family: {{BODY_DEFAULT_FONT_FAMILY}};
            position: relative;
        }
        
        #textDisplayWrapper {
            position: absolute;
            {{TEXT_WRAPPER_POSITION_CSS}}
            min-height: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: {{TEXT_WRAPPER_Z_INDEX}};
        }

        .text-display {
            line-height: 1.2;
            word-wrap: break-word;
            display: inline-block;
            text-align: center;
            -webkit-paint-order: stroke fill;
            paint-order: stroke fill;
            /* 所有动态样式（font-size, color, text-shadow, z-index）都将被移除 */
            /* 只保留结构性样式 */
        }

        .sub-line {
            /* 动态样式将被JS应用 */
            display: none; /* 初始隐藏 */
            padding: 2px 8px;
            border-radius: 5px;
        }

        .animated {
            /* 动画定义保持不变，但使用CSS变量 */
            animation: pulse var(--pulse-duration, 0.3s) ease-in-out infinite alternate;
        }

        @keyframes pulse {
            from {
                transform: scale(var(--pulse-scale-from, 0.9));
                opacity: var(--pulse-opacity-from, 1.0);
            }
            to {
                transform: scale(var(--pulse-scale-to, 1.1));
                opacity: var(--pulse-opacity-to, 1.0);
            }
        }
        
        {{QUEUE_STYLES_BLOCK}}

        #grabAreaCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: {{GRAB_AREA_CANVAS_Z_INDEX}};
            pointer-events: none;
        }

        /* 等待订单提示样式 - 位置由JS动态设置 */
        #waitingOrderDisplayWrapper {
            position: absolute;
            /* 位置由JS根据配置动态设置 */
            transform: translate(-50%, -50%);
            z-index: {{TEXT_WRAPPER_Z_INDEX}};
            text-align: center;
        }

        .waiting-order-display {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 8px;
            visibility: hidden; /* 默认隐藏 */
        }
    </style>
</head>
<body>
    <canvas id="grabAreaCanvas"></canvas>

    <div id="textDisplayWrapper">
        <div id="textDisplay" class="text-display"></div>
    </div>

    <!-- 新增：等待订单提示专用显示区域 -->
    <div id="waitingOrderDisplayWrapper">
        <div id="waitingOrderDisplay" class="waiting-order-display"></div>
    </div>

    <div id="queueDisplayContainer">
        <div class="queue-header" id="queueHeader"></div>
        <table class="queue-table" id="queueTable">
            <thead id="queueTableHead"></thead>
            <tbody id="queueTableBody"></tbody>
        </table>
        <div class="queue-footer" id="queueFooter"></div>
    </div>

    <script>
        let displaySettings = {{DISPLAY_SETTINGS_JSON}};
        let grabAreaConfig = {{GRAB_AREA_CONFIG_JSON}};
        let queueDisplayConfig = {{QUEUE_DISPLAY_CONFIG_JSON}}; // 新增
        let lastQueueData = null; // 新增，用于刷新队列显示

        let activeTimers = { main: null, sub1: null, sub2: null, general: null, waitingOrder: null };
        const grabAreaCanvas = document.getElementById('grabAreaCanvas');
        const grabAreaCtx = grabAreaCanvas.getContext('2d');

        function resizeGrabAreaCanvas() {
            grabAreaCanvas.width = window.innerWidth;
            grabAreaCanvas.height = window.innerHeight;
        }
        window.addEventListener('resize', resizeGrabAreaCanvas);

        function clearAllTimers() {
            for (const key in activeTimers) {
                if (activeTimers[key]) {
                    clearTimeout(activeTimers[key]);
                    activeTimers[key] = null;
                }
            }
        }

        function updateGrabAreaDisplay(data) {
            if (!grabAreaConfig.enabled) {
                grabAreaCtx.clearRect(0, 0, grabAreaCanvas.width, grabAreaCanvas.height);
                return;
            }
            if (data.config) grabAreaConfig = data.config;
            const viewTransform = grabAreaConfig.view_transform || {};
            const scale = viewTransform.scale || 1.0;
            const rotationDegrees = viewTransform.rotation_degrees || 0.0;
            const rotationRadians = rotationDegrees * Math.PI / 180;
            const centerRatio = viewTransform.center_on_webview_ratio || [0.5, 0.5];
            grabAreaCtx.clearRect(0, 0, grabAreaCanvas.width, grabAreaCanvas.height);
            grabAreaCtx.save();
            let roiCenterX = 0, roiCenterY = 0;
            const roiBoundaryPoints = data.roi_boundary_points_transformed;
            if (roiBoundaryPoints && roiBoundaryPoints.length > 0) {
                let sumX = 0, sumY = 0;
                roiBoundaryPoints.forEach(p => { sumX += p[0]; sumY += p[1]; });
                roiCenterX = sumX / roiBoundaryPoints.length;
                roiCenterY = sumY / roiBoundaryPoints.length;
            }
            const targetCanvasX = grabAreaCanvas.width * centerRatio[0];
            const targetCanvasY = grabAreaCanvas.height * centerRatio[1];
            grabAreaCtx.translate(targetCanvasX, targetCanvasY);
            grabAreaCtx.rotate(rotationRadians);
            grabAreaCtx.scale(scale, scale);
            grabAreaCtx.translate(-roiCenterX, -roiCenterY);
            const roiDisplayConfig = grabAreaConfig.roi_boundary_display || {};
            if (roiDisplayConfig.enabled && roiBoundaryPoints && roiBoundaryPoints.length >= 3) {
                grabAreaCtx.beginPath();
                grabAreaCtx.moveTo(roiBoundaryPoints[0][0], roiBoundaryPoints[0][1]);
                for (let i = 1; i < roiBoundaryPoints.length; i++) grabAreaCtx.lineTo(roiBoundaryPoints[i][0], roiBoundaryPoints[i][1]);
                grabAreaCtx.closePath();
                grabAreaCtx.strokeStyle = roiDisplayConfig.color || 'yellow';
                grabAreaCtx.lineWidth = roiDisplayConfig.line_width || 2;
                grabAreaCtx.stroke();
            }
            const objectDisplayConfig = grabAreaConfig.object_display || {};
            const labelDisplayConfig = grabAreaConfig.label_display || {};
            if (objectDisplayConfig.enabled && data.objects) {
                data.objects.forEach(obj => {
                    const box = obj.bbox_transformed_axis_aligned;
                    const width = box[2] - box[0];
                    const height = box[3] - box[1];
                    let color = objectDisplayConfig.default_bounding_box_color || 'orange';
                    if (objectDisplayConfig.bounding_box_colors_by_class_id && obj.class_id !== undefined && objectDisplayConfig.bounding_box_colors_by_class_id.hasOwnProperty(obj.class_id)) {
                        color = objectDisplayConfig.bounding_box_colors_by_class_id[obj.class_id];
                    }
                    grabAreaCtx.strokeStyle = color;
                    grabAreaCtx.lineWidth = objectDisplayConfig.bounding_box_line_width || 2;
                    grabAreaCtx.strokeRect(box[0], box[1], width, height);
                    if (labelDisplayConfig.enabled && obj.id !== undefined) {
                        const labelText = String(obj.id);
                        let anchorX, anchorY;
                        if (Math.abs(rotationDegrees - 90) < 45) { anchorX = box[0]; anchorY = box[1] + height/2; }
                        else if (Math.abs(rotationDegrees - 180) < 45) { anchorX = box[0] + width/2; anchorY = box[1] + height; }
                        else if (Math.abs(rotationDegrees - 270) < 45 || Math.abs(rotationDegrees + 90) < 45) { anchorX = box[0] + width; anchorY = box[1] + height/2; }
                        else { anchorX = box[0] + width/2; anchorY = box[1]; }
                        const labelX = anchorX + (labelDisplayConfig.offset_x_px || 0);
                        const labelY = anchorY + (labelDisplayConfig.offset_y_px || 0);
                        const cosAngle = Math.cos(rotationRadians), sinAngle = Math.sin(rotationRadians);
                        const p_relative_x = labelX - roiCenterX, p_relative_y = labelY - roiCenterY;
                        const p_scaled_x = p_relative_x * scale, p_scaled_y = p_relative_y * scale;
                        const p_rotated_x = p_scaled_x * cosAngle - p_scaled_y * sinAngle;
                        const p_rotated_y = p_scaled_x * sinAngle + p_scaled_y * cosAngle;
                        const finalX = p_rotated_x + targetCanvasX, finalY = p_rotated_y + targetCanvasY;
                        grabAreaCtx.save();
                        grabAreaCtx.setTransform(1, 0, 0, 1, 0, 0);
                        grabAreaCtx.font = `${labelDisplayConfig.font_size_px || 12}px ${labelDisplayConfig.font_family || 'Arial'}`;
                        grabAreaCtx.textAlign = 'center';
                        grabAreaCtx.textBaseline = 'bottom';
                        if (labelDisplayConfig.stroke_color && (labelDisplayConfig.stroke_width_px || 0) > 0) {
                            grabAreaCtx.strokeStyle = labelDisplayConfig.stroke_color;
                            grabAreaCtx.lineWidth = labelDisplayConfig.stroke_width_px;
                            grabAreaCtx.lineJoin = "round";
                            grabAreaCtx.lineCap = "round";
                            grabAreaCtx.strokeText(labelText, finalX, finalY);
                        }
                        grabAreaCtx.fillStyle = labelDisplayConfig.font_color || 'white';
                        grabAreaCtx.fillText(labelText, finalX, finalY);
                        grabAreaCtx.restore();
                    }
                });
            }
            grabAreaCtx.restore();
        }

        function isEmoji(char) {
            const emojiRegex = /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{2B50}\u{23E9}-\u{23FA}\u{231A}-\u{231B}\u{25FD}-\u{25FE}]/u;
            return emojiRegex.test(char);
        }

        // --- 核心修改：新的样式应用函数 ---

        /**
         * 应用所有动态样式到一个元素上
         * @param {HTMLElement} element - 要应用样式的DOM元素
         * @param {object} config - 对应的配置对象 (e.g., displaySettings.playing)
         * @param {string} namePrefix - 字体名称前缀 (e.g., "Playing")
         */
        function applyElementStyles(element, config, namePrefix) {
            if (!element || !config) return;

            element.style.fontFamily = `'${namePrefix}EmojiCustomFont', '${namePrefix}RegularCustomFont', 'Microsoft YaHei', sans-serif`;
            element.style.fontSize = config.font_size || '30px';
            element.style.color = config.font_color || '#FFFFFF';
            element.style.zIndex = config.z_index || 20;
            element.style.textShadow = createTextShadow(
                config.stroke_width || '0px',
                config.shadow_blur || '0px',
                config.stroke_color || '#000000'
            );

            if (config.animated) {
                element.classList.add('animated');
                element.style.setProperty('--pulse-duration', (config.pulse_duration_seconds || 0.3) + 's');
                element.style.setProperty('--pulse-scale-from', config.pulse_scale_from || 0.9);
                element.style.setProperty('--pulse-scale-to', config.pulse_scale_to || 1.1);
                element.style.setProperty('--pulse-opacity-from', config.pulse_opacity_from || 1.0);
                element.style.setProperty('--pulse-opacity-to', config.pulse_opacity_to || 1.0);
            } else {
                element.classList.remove('animated');
            }
        }

        /**
         * 应用子行特有的样式
         * @param {HTMLElement} element - 子行div
         * @param {object} config - 子行的配置对象
         * @param {number} index - 子行的索引 (0 or 1)
         */
        function applySubLineStyles(element, config, index) {
            if (!element || !config) return;
            element.style.fontFamily = `'SubLine${index}RegularCustomFont', 'Microsoft YaHei', sans-serif`;
            element.style.fontSize = config.font_size || '20px';
            element.style.color = config.font_color || '#FFFFFF';
            element.style.backgroundColor = config.background_color || 'transparent';
            element.style.marginTop = config.margin_top || '10px';
            element.style.zIndex = config.z_index || 22;
            element.style.textShadow = createTextShadow(
                config.stroke_width || '0px',
                config.shadow_blur || '0px',
                config.stroke_color || '#000000'
            );
            if (config.animated) element.classList.add('animated');
            else element.classList.remove('animated');
        }

        // --- 重写 updateStatusMessage ---
        function updateStatusMessage(statusType, playerName, itemName, successCount) {
            // 特殊处理 player_waiting_order 类型
            if (statusType === 'player_waiting_order') {
                const waitingOrderWrapper = document.getElementById('waitingOrderDisplayWrapper');
                const waitingOrderDisplay = document.getElementById('waitingOrderDisplay');
                clearTimeout(activeTimers.waitingOrder); // 清除之前的定时器

                // 获取配置
                const config = displaySettings[statusType];
                if (!config || !config.enabled) return;

                // 应用位置配置 - 使用config中的center和width参数
                if (config.center && Array.isArray(config.center) && config.center.length === 2) {
                    waitingOrderWrapper.style.left = config.center[0];
                    waitingOrderWrapper.style.top = config.center[1];
                }

                if (config.width) {
                    waitingOrderWrapper.style.width = config.width;
                }

                // 应用样式
                applyElementStyles(waitingOrderDisplay, config, 'WaitingOrder');

                // 设置内容
                let text = config.template || '';
                text = text.replace('{player}', playerName || '玩家');
                waitingOrderDisplay.innerHTML = text.replace(/\n/g, '<br>');

                // 显示
                waitingOrderDisplay.style.visibility = 'visible';

                // 设置定时器自动隐藏
                if (config.duration > 0) {
                    activeTimers.waitingOrder = setTimeout(() => {
                        waitingOrderDisplay.style.visibility = 'hidden';
                    }, config.duration * 1000);
                }

                return; // 提前返回，不处理其他类型
            }

            const container = document.getElementById('textDisplay');
            container.dataset.status = statusType; // 新增：在这里明确记录当前状态
            clearAllTimers();
            container.innerHTML = ''; // 清空容器

            const config = displaySettings[statusType];
            if (!config) return;

            if (statusType === 'caught_item') {
                const mainLineConfig = config;
                const subLinesConfig = config.sub_lines || {};

                // 创建并设置主行样式
                const mainLineDiv = document.createElement('div');
                mainLineDiv.id = 'main-line-div';
                mainLineDiv.className = 'text-display';
                applyElementStyles(mainLineDiv, mainLineConfig, 'CaughtItem');
                let mainText = mainLineConfig.template || '';
                mainText = mainText.replace('{player}', playerName || '玩家').replace('{item}', itemName || '');
                mainLineDiv.innerHTML = mainText.replace(/\n/g, '<br>');
                container.appendChild(mainLineDiv);
                if (mainLineConfig.duration > 0) {
                    activeTimers.main = setTimeout(() => {
                        mainLineDiv.style.display = 'none';
                    }, mainLineConfig.duration * 1000);
                }

                // 创建并设置子行样式
                if (subLinesConfig.lines && Array.isArray(subLinesConfig.lines)) {
                    subLinesConfig.lines.forEach((subLineCfg, index) => {
                        if (subLineCfg.enabled && index < 2) {
                            const subLineDiv = document.createElement('div');
                            subLineDiv.id = `sub-line-${index}-div`;
                            subLineDiv.className = 'text-display sub-line';
                            applySubLineStyles(subLineDiv, subLineCfg, index);

                            let subText = '';
                            // 检查是否存在任何 templateX 格式的键
                            const hasNumberedTemplates = Object.keys(subLineCfg).some(k => /^template\d+$/.test(k));

                            if (hasNumberedTemplates) {
                                // 尝试直接匹配 template{successCount}
                                const directMatchKey = `template${successCount}`;
                                if (subLineCfg[directMatchKey]) {
                                    subText = subLineCfg[directMatchKey];
                                } else {
                                    // 如果不匹配，则查找可用的最高级模板
                                    let maxNum = 0;
                                    for (const key in subLineCfg) {
                                        if (/^template\d+$/.test(key)) {
                                            const num = parseInt(key.replace('template', ''), 10);
                                            if (num > maxNum) {
                                                maxNum = num;
                                            }
                                        }
                                    }
                                    if (maxNum > 0) {
                                        subText = subLineCfg[`template${maxNum}`] || '';
                                    } else {
                                        // 如果没有找到任何有效的 templateX，回退到通用 template
                                        subText = subLineCfg.template || '';
                                    }
                                }
                            } else {
                                // 如果不存在任何 templateX 键，直接使用通用 template
                                subText = subLineCfg.template || '';
                            }

                            if (!subText) return;
                            subText = subText.replace('{player}', playerName || '玩家').replace('{item}', itemName || '');
                            subLineDiv.innerHTML = subText.replace(/\n/g, '<br>');
                            subLineDiv.style.display = 'inline-block';
                            container.appendChild(subLineDiv);
                            if (subLineCfg.duration > 0) {
                                activeTimers[`sub${index+1}`] = setTimeout(() => {
                                    subLineDiv.style.display = 'none';
                                }, subLineCfg.duration * 1000);
                            }
                        }
                    });
                }
            } else {
                // 对于 playing 和 caught_nothing
                const textDiv = document.createElement('div');
                textDiv.id = 'main-line-div'; // 给它一个统一的ID
                textDiv.className = 'text-display';
                const namePrefix = statusType === 'playing' ? 'Playing' : 'CaughtNothing';
                applyElementStyles(textDiv, config, namePrefix);
                let text = config.template || '';
                text = text.replace('{player}', playerName || '玩家').replace('{item}', itemName || '');
                textDiv.innerHTML = text.replace(/\n/g, '<br>');
                container.appendChild(textDiv);
                if (config.duration > 0) {
                    activeTimers.main = setTimeout(() => {
                        textDiv.innerHTML = '';
                    }, config.duration * 1000);
                }
            }
        }

        function updateText(text, animated, duration) {
            const container = document.getElementById('textDisplay');
            clearAllTimers();
            container.innerHTML = '';
            if (!text) {
                container.className = 'text-display';
                // --- 修复：清除状态 ---
                delete container.dataset.status;
                return;
            }

            // --- 修复：为通用文本设置状态，以便刷新 ---
            // 假设通用消息复用 'playing' 状态的样式
            container.dataset.status = 'playing';

            // 使用JavaScript应用样式，而不是CSS类
            const textDiv = document.createElement('div');
            textDiv.id = 'main-line-div';
            textDiv.className = 'text-display';

            // 使用playing状态的配置
            const playingConfig = displaySettings.playing || {};
            applyElementStyles(textDiv, playingConfig, 'Playing');

            textDiv.innerHTML = text.replace(/\n/g, '<br>');
            container.appendChild(textDiv);

            if (duration && duration > 0) {
                activeTimers.general = setTimeout(() => {
                    textDiv.innerHTML = '';
                }, duration * 1000);
            }
        }

        function formatPlayerName(name) {
            const hasEmoji = Array.from(name).some(char => isEmoji(char));
            const className = hasEmoji ? 'queue-name-emoji' : 'queue-name-regular';
            return `<span class="${className}">${name}</span>`;
        }

        function updateQueueDisplay(data) {
            if (data) { // 只有在有新数据时才更新
                lastQueueData = data; // 保存最新的队列数据
            }
            const container = document.getElementById('queueDisplayContainer');
            const header = document.getElementById('queueHeader');
            const tableHead = document.getElementById('queueTableHead');
            const tableBody = document.getElementById('queueTableBody');
            const footer = document.getElementById('queueFooter');
            if (!data || !data.items || data.items.length === 0) {
                container.style.display = 'none';
                return;
            }
            header.innerHTML = (data.header_text || '排队网友').replace(/\n/g, '<br>');
            tableHead.innerHTML = '';
            if (data.show_headers) {
                const headerRow = document.createElement('tr');
                headerRow.innerHTML = `
                    <th class="col-index">${(data.headers && data.headers.index) || 'No.'}</th>
                    <th class="col-name">${(data.headers && data.headers.name) || '家人'}</th>
                    <th class="col-comments">${(data.headers && data.headers.comments) || '评论数'}</th>
                    <th class="col-time">${(data.headers && data.headers.time) || '时长'}</th>
                `;
                tableHead.appendChild(headerRow);
            }
            tableBody.innerHTML = '';
            data.items.forEach(item => {
                const row = document.createElement('tr');
                const isPaid = item.order_id !== null;

                // 直接在这里处理样式，而不是用class
                const indexCell = document.createElement('td');
                indexCell.className = 'col-index';
                indexCell.textContent = item.index;

                const nameCell = document.createElement('td');
                nameCell.className = 'col-name';
                nameCell.innerHTML = formatPlayerName(item.name);

                // 动态应用付费玩家样式
                if (isPaid && queueDisplayConfig.paid_player_style) {
                    const style = queueDisplayConfig.paid_player_style;
                    if (style.bold) {
                        nameCell.style.fontWeight = 'bold';
                    }
                    if (style.font_color) {
                        nameCell.style.color = style.font_color;
                    }
                }

                const commentsCell = document.createElement('td');
                commentsCell.className = 'col-comments';
                commentsCell.textContent = item.comments_count;

                const timeCell = document.createElement('td');
                timeCell.className = 'col-time';
                timeCell.textContent = item.time_min;

                row.appendChild(indexCell);
                row.appendChild(nameCell);
                row.appendChild(commentsCell);
                row.appendChild(timeCell);
                tableBody.appendChild(row);
            });
            footer.innerHTML = (data.footer_text || '').replace(/\n/g, '<br>');
            container.style.display = 'block';
        }

        const WEBSOCKET_PORT = displaySettings.websocket_port || 5560;
        let socket;

        function connectWebSocket() {
            socket = new WebSocket(`ws://127.0.0.1:${WEBSOCKET_PORT}`);
            socket.onopen = () => console.log("[WebSocket] 连接已建立");
            socket.onmessage = (event) => {
                try {
                    const command = JSON.parse(event.data);
                    handleCommand(command);
                } catch (error) {
                    console.error("[WebSocket] 解析消息失败:", error);
                }
            };
            socket.onclose = () => setTimeout(connectWebSocket, 3000);
            socket.onerror = (err) => {
                console.error('[WebSocket] 发生错误: ', err.message);
                socket.close();
            };
        }

        function handleCommand(command) {
            switch(command.action) {
                case 'show_message':
                    updateText(command.text, command.animated, command.duration);
                    break;
                case 'show_status_message':
                    updateStatusMessage(command.status_type, command.player_name, command.item_name, command.success_count);
                    break;
                case 'update_queue':
                    updateQueueDisplay(command.data);
                    break;
                case 'clear_text':
                    updateText('', false, null);
                    break;
                case 'update_detection_display':
                    updateGrabAreaDisplay(command.data);
                    break;
                case 'update_config':
                    // 全面替换旧的 update_config 逻辑
                    if (command.config && command.config.web_display) {
                        console.log("收到配置更新:", command.config.web_display);
                        applyDynamicConfigs(command.config.web_display);
                    }
                    break;
            }
        }

        // --- 新增：应用动态配置的核心函数 ---

        /**
         * 主函数，根据收到的新配置更新页面所有动态元素
         * @param {object} newConfig - web_display部分的配置对象
         */
        function applyDynamicConfigs(newConfig) {
            const wrapper = document.getElementById('textDisplayWrapper');
            const grabCanvas = document.getElementById('grabAreaCanvas');

            // 1. 更新顶层通用样式
            if (newConfig.background_color) {
                document.body.style.backgroundColor = newConfig.background_color;
            }
            if (newConfig.text_wrapper_z_index) {
                wrapper.style.zIndex = newConfig.text_wrapper_z_index;
            }
            if (newConfig.grab_area_canvas_z_index) {
                grabCanvas.style.zIndex = newConfig.grab_area_canvas_z_index;
            }

            // 2. 更新 display_settings (提示词样式和位置)
            if (newConfig.display_settings) {
                displaySettings = deepMerge(displaySettings, newConfig.display_settings);
                if (displaySettings.position) {
                    if (displaySettings.position.center) {
                        wrapper.style.left = displaySettings.position.center[0];
                        wrapper.style.top = displaySettings.position.center[1];
                    }
                    if (displaySettings.position.width) {
                        wrapper.style.width = displaySettings.position.width;
                    }
                }

                // --- 修正：使用 data-status 属性来可靠地刷新当前样式 ---
                const textContainer = document.getElementById('textDisplay');
                const currentStatus = textContainer.dataset.status; // 读取我们记录的状态

                if (currentStatus && displaySettings[currentStatus]) {
                    const mainLineDiv = document.getElementById('main-line-div');
                    if (mainLineDiv) {
                        // 根据状态动态确定字体前缀
                        const namePrefix = currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1).replace(/_([a-z])/g, g => g[1].toUpperCase());
                        applyElementStyles(mainLineDiv, displaySettings[currentStatus], namePrefix);
                    }
                    // 刷新子行
                    if (currentStatus === 'caught_item') {
                        for (let i = 0; i < 2; i++) {
                            const subLineDiv = document.getElementById(`sub-line-${i}-div`);
                            if (subLineDiv && displaySettings.caught_item.sub_lines) {
                                const subLineConfig = displaySettings.caught_item.sub_lines.lines[i];
                                if (subLineConfig) {
                                    applySubLineStyles(subLineDiv, subLineConfig, i);
                                }
                            }
                        }
                    }
                }

                // 更新等待订单显示区域的配置
                if (newConfig.display_settings && newConfig.display_settings.player_waiting_order) {
                    const waitingOrderConfig = newConfig.display_settings.player_waiting_order;
                    const waitingOrderWrapper = document.getElementById('waitingOrderDisplayWrapper');

                    if (waitingOrderConfig.center &&
                        Array.isArray(waitingOrderConfig.center) &&
                        waitingOrderConfig.center.length === 2) {
                        waitingOrderWrapper.style.left = waitingOrderConfig.center[0];
                        waitingOrderWrapper.style.top = waitingOrderConfig.center[1];
                    }

                    if (waitingOrderConfig.width) {
                        waitingOrderWrapper.style.width = waitingOrderConfig.width;
                    }
                }
            }

            // 3. 更新 queue_display (排队列表样式)
            if (newConfig.queue_display) {
                queueDisplayConfig = deepMerge(queueDisplayConfig, newConfig.queue_display);
                applyQueueStyles(queueDisplayConfig);
                if(lastQueueData) {
                    updateQueueDisplay(null); // 使用保存的数据强制重绘
                }
            }

            // 4. 更新 grab_area (抓取区域可视化)
            if (newConfig.grab_area) {
                grabAreaConfig = deepMerge(grabAreaConfig, newConfig.grab_area);
            }

            console.log("动态配置已应用");
        }

        /**
         * 应用排队列表的样式
         * @param {object} qConfig - queue_display 的配置对象
         */
        function applyQueueStyles(qConfig) {
            const container = document.getElementById('queueDisplayContainer');
            const header = document.getElementById('queueHeader');
            const footer = document.getElementById('queueFooter');
            if (!container) return;

            container.style.display = qConfig.enabled ? 'block' : 'none';
            if (!qConfig.enabled) return;

            container.style.left = qConfig.position_x || '0px';
            container.style.top = qConfig.position_y || '0px';
            container.style.zIndex = qConfig.z_index || 10;
            container.style.fontSize = qConfig.font_size || '24px';
            container.style.color = qConfig.font_color || '#ffffff';
            container.style.borderRadius = qConfig.border_radius || '8px';
            container.style.padding = qConfig.padding || '10px';

            const textShadow = createTextShadow(
                qConfig.stroke_width || '2px',
                qConfig.shadow_blur || '2px',
                qConfig.stroke_color || '#000000'
            );
            container.style.textShadow = textShadow;

            // 新增：动态更新表头和表尾的样式
            if (header && qConfig.headers) {
                const baseFontSize = parseInt(qConfig.font_size || '24px', 10);
                const multiplier = qConfig.headers.text_before_font_size_multiplier || 1.0;
                header.style.fontSize = (baseFontSize * multiplier) + 'px';
                header.style.marginLeft = (qConfig.headers.text_before_margin_left_px || 0) + 'px';
            }

            if (footer) {
                const baseFontSize = parseInt(qConfig.font_size || '24px', 10);
                // 这里的 1.0 乘数与 Python 后端代码保持一致
                footer.style.fontSize = (baseFontSize * 1.0) + 'px';
            }
        }

        /**
         * 根据描边和阴影参数生成CSS text-shadow值
         */
        function createTextShadow(strokeWidth, shadowBlur, strokeColor) {
            if (!strokeWidth || strokeWidth === '0px') return 'none';
            const s = strokeWidth;
            const b = shadowBlur;
            const c = strokeColor;
            return `- ${s} - ${s} ${b} ${c}, - ${s} 0px ${b} ${c}, - ${s} ${s} ${b} ${c}, 0px - ${s} ${b} ${c}, 0px ${s} ${b} ${c}, ${s} - ${s} ${b} ${c}, ${s} 0px ${b} ${c}, ${s} ${s} ${b} ${c}`;
        }

        /**
         * 深层合并两个对象，用于安全地更新配置
         */
        function deepMerge(target, source) {
            const output = { ...target };
            if (isObject(target) && isObject(source)) {
                Object.keys(source).forEach(key => {
                    if (isObject(source[key])) {
                        if (!(key in target))
                            Object.assign(output, { [key]: source[key] });
                        else
                            output[key] = deepMerge(target[key], source[key]);
                    } else {
                        Object.assign(output, { [key]: source[key] });
                    }
                });
            }
            return output;
        }

        function isObject(item) {
            return (item && typeof item === 'object' && !Array.isArray(item));
        }

        // --- 初始化 ---
        document.addEventListener('DOMContentLoaded', () => {
            resizeGrabAreaCanvas();
            applyQueueStyles(queueDisplayConfig); // 页面加载时应用一次初始队列样式

            // 初始化等待订单显示区域的位置
            const waitingOrderConfig = displaySettings.player_waiting_order;
            if (waitingOrderConfig && waitingOrderConfig.center &&
                Array.isArray(waitingOrderConfig.center) && waitingOrderConfig.center.length === 2) {
                const waitingOrderWrapper = document.getElementById('waitingOrderDisplayWrapper');
                waitingOrderWrapper.style.left = waitingOrderConfig.center[0];
                waitingOrderWrapper.style.top = waitingOrderConfig.center[1];

                if (waitingOrderConfig.width) {
                    waitingOrderWrapper.style.width = waitingOrderConfig.width;
                }
            }

            connectWebSocket();
        });

        window.addEventListener('pywebviewready', () => {
            window.handleCommand = handleCommand;
            resizeGrabAreaCanvas();
            updateText("", false, null);
        });

        connectWebSocket();
        setTimeout(resizeGrabAreaCanvas, 200);
    </script>
</body>
</html>
